<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(backlink_path) %>
<% end %>

<% legend = "Do you agree to your child having the MenACWY and Td/IPV (3-in-1 teenage booster) vaccinations?" %>
<% content_for :page_title, legend %>

<%= form_with model: @consent_form, url: wizard_path, method: :put do |f| %>
  <%= f.govuk_error_summary %>

  <%= f.govuk_radio_buttons_fieldset :response, legend: { size: "l", text: legend, tag: "h1" } do %>
    <%= f.govuk_radio_button :response, "given",
                             label: { text: "Yes, I agree to them having both vaccinations" },
                             link_errors: true %>
    <%= f.govuk_radio_button :response, "given_one",
                             label: { text: "I agree to them having one of the vaccinations" } do %>
      <%= f.govuk_radio_buttons_fieldset :chosen_programme,
                                         legend: {
                                           size: "s",
                                           text: "Which vaccinations do you give consent for?",
                                         } do %>
        <% @consent_form.programmes.each do |programme| %>
          <%= f.govuk_radio_button :chosen_programme, programme.type,
                                   label: { text: programme.name } %>
        <% end %>
      <% end %>
    <% end %>

    <%= f.govuk_radio_button :response, "refused", label: { text: "No" } %>
  <% end %>

  <%= f.govuk_submit "Continue" %>
<% end %>
