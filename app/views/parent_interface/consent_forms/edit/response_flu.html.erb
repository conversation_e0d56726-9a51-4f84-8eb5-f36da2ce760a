<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(backlink_path) %>
<% end %>

<% legend = "Do you agree to your child having the flu vaccination in school?" %>
<% content_for :page_title, legend %>

<%= form_with model: @consent_form, url: wizard_path, method: :put do |f| %>
  <%= f.govuk_error_summary %>

  <%= f.govuk_radio_buttons_fieldset :response,
                                     legend: { size: "l", text: legend, tag: "h1" },
                                     hint: { text: "The nasal flu spray contains gelatine which comes from pigs." } do %>

    <%= f.govuk_radio_button :response, "given_nasal",
                             label: { text: "Yes, I agree to them having the nasal spray vaccine" },
                             hint: { text: "This is the recommended option and gives the best protection against flu" },
                             link_errors: true %>

    <%= f.govuk_radio_button :response, "given_injection",
                             label: { text: "Yes, I agree to the alternative flu injection" },
                             hint: { text: "This is suitable for children who do not use gelatine products, or if they cannot have the nasal spray vaccine for medical reasons" } %>

    <%= f.govuk_radio_button :response, "refused",
                             label: { text: "No" } %>
  <% end %>

  <%= f.govuk_submit "Continue" %>
<% end %>
