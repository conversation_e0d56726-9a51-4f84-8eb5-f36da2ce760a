<%= h1 t(".title"), size: "xl" %>

<% if @consent_forms.any? %>
  <div class="nhsuk-table__panel-with-heading-tab">
    <h3 class="nhsuk-table__heading-tab">
      <%= pluralize(@pagy.count, "unmatched consent response") %>
    </h3>

    <%= govuk_table(html_attributes: { class: "nhsuk-table-responsive" }) do |table| %>
      <%= table.with_head do |head|
            head.with_row do |row|
              row.with_cell(text: "Response")
              row.with_cell(text: "Response date")
              row.with_cell(text: "Decision")
              row.with_cell(text: "Actions")
            end
          end %>

      <%= table.with_body do |body| %>
        <% @consent_forms.each do |consent_form| %>
          <%= body.with_row do |row| %>
            <%= row.with_cell do %>
              <span class="nhsuk-table-responsive__heading">Response</span>
              <span>
                <%= link_to consent_form.parent_relationship.label_with_parent, consent_form_path(consent_form) %>
                <br>
                <span class="nhsuk-u-secondary-text-color nhsuk-u-font-size-16">
                  for <%= consent_form.full_name %>
                </span>
              </span>
            <% end %>

            <%= row.with_cell do %>
              <span class="nhsuk-table-responsive__heading">Response date</span>
              <%= consent_form.recorded_at.to_date.to_fs(:long) %>
            <% end %>

            <%= row.with_cell do %>
              <span class="nhsuk-table-responsive__heading">Decision</span>
              <%= consent_form.human_enum_name(:response) %>
            <% end %>

            <%= row.with_cell do %>
              <span class="nhsuk-table-responsive__heading">Decision</span>
              <ul class="app-action-list">
                <li class="app-action-list__item">
                  <%= link_to "Match", search_consent_form_path(consent_form) %>
                </li>

                <li class="app-action-list__item">
                  <%= link_to "Archive", archive_consent_form_path(consent_form) %>
                </li>
              </ul>
            <% end %>
          <% end %>
        <% end %>
      <% end %>
    <% end %>
  </div>

  <%= govuk_pagination(pagy: @pagy) %>
<% else %>
  <p class="nhsuk-body">There are currently no unmatched consent responses.</p>
<% end %>
