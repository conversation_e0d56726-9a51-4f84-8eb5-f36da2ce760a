<%= h1 t(".title"), size: "xl" %>

<% @vaccines.each do |vaccine| %>
  <%= render AppCardComponent.new(section: true) do |card| %>
    <% card.with_heading { link_to(vaccine_heading(vaccine), vaccine_path(vaccine)) } %>

    <p><%= vaccine.manufacturer %></p>

    <%= govuk_button_link_to "Add a new batch",
                             new_vaccine_batch_path(vaccine),
                             secondary: true,
                             aria: { label: "Add a new #{vaccine.brand} batch" } %>

    <% if (batches = @batches_by_vaccine_id[vaccine.id]).present? %>
      <%= govuk_table do |table| %>
        <%= table.with_head do |head| %>
          <%= head.with_row do |row| %>
            <%= row.with_cell(text: "Batch") %>
            <%= row.with_cell(text: "Entered date") %>
            <%= row.with_cell(text: "Expiry date") %>
            <%= row.with_cell(text: "Actions") %>
          <% end %>
        <% end %>

        <%= table.with_body do |body| %>
          <% batches.each do |batch| %>
            <% body.with_row do |row| %>
              <% row.with_cell(text: batch.name) do %>
                <%= batch.name %>
                <% if batch.id == @todays_batch_id_by_programme[vaccine.programme] %>
                  <br>
                  <span class="nhsuk-caption-m">
                    (Your default)
                  </span>
                <% end %>
              <% end %>
              <% row.with_cell(text: batch.created_at.to_date.to_fs(:long)) %>
              <% row.with_cell(text: batch.expiry&.to_fs(:long) || "Unknown") %>
              <% row.with_cell do %>
                <ul class="app-action-list">
                  <li class="app-action-list__item">
                    <%= link_to "Change", edit_vaccine_batch_path(vaccine, batch),
                                aria: { label: "Change #{batch.name} batch of #{vaccine.brand}" } %>
                  </li>
                  <li class="app-action-list__item">
                    <%= link_to "Archive", archive_vaccine_batch_path(vaccine, batch),
                                aria: { label: "Archive #{batch.name} batch of #{vaccine.brand}" } %>
                  </li>
                </ul>
              <% end %>
            <% end %>
          <% end %>
        <% end %>
      <% end %>
    <% end %>
  <% end %>
<% end %>
